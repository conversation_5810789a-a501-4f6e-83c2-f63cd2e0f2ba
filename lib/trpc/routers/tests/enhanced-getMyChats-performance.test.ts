/**
 * Performance test for enhanced getMyChats with real data integration
 * 
 * This test validates that the enhanced getMyChats procedure with unread counts
 * and verification status performs well and returns correct data.
 */

import { describe, it, expect, beforeAll, afterAll } from "bun:test";
import { db } from "@/lib/db";
import { chats, chatParticipants, messages, users, cats, catImages } from "@/lib/db/schema";
import { eq, inArray, desc, and } from "drizzle-orm";
import { chatHelpers } from "../helpers/chat-helpers";

// Mock data for testing
const testData = {
	users: [] as any[],
	cats: [] as any[],
	chats: [] as any[],
	messages: [] as any[],
};

describe("Enhanced getMyChats Performance Test", () => {
	beforeAll(async () => {
		// Create test users with different roles
		const testUsers = await db.insert(users).values([
			{
				name: "Regular User",
				email: "<EMAIL>",
				slug: "regular-user",
				role: "adopter",
			},
			{
				name: "Verified Rescuer",
				email: "<EMAIL>",
				slug: "verified-rescuer",
				role: "rescuer",
			},
			{
				name: "Verified Clinic",
				email: "<EMAIL>",
				slug: "verified-clinic",
				role: "clinic",
			},
		]).returning();
		testData.users = testUsers;

		// Create test cats
		const testCats = await db.insert(cats).values([
			{
				name: "Test Cat 1",
				slug: "test-cat-1",
				userId: testUsers[1].id, // Rescuer's cat
				age: 2,
				gender: "male",
				description: "A friendly cat",
				wilayaId: 1,
				communeId: 1,
			},
			{
				name: "Test Cat 2",
				slug: "test-cat-2",
				userId: testUsers[2].id, // Clinic's cat
				age: 3,
				gender: "female",
				description: "A playful cat",
				wilayaId: 1,
				communeId: 1,
			},
		]).returning();
		testData.cats = testCats;

		// Create test chats
		const testChats = await db.insert(chats).values([
			{ catId: testCats[0].id },
			{ catId: testCats[1].id },
		]).returning();
		testData.chats = testChats;

		// Create chat participants
		await db.insert(chatParticipants).values([
			{ chatId: testChats[0].id, userId: testUsers[0].id }, // Regular user
			{ chatId: testChats[0].id, userId: testUsers[1].id }, // Rescuer
			{ chatId: testChats[1].id, userId: testUsers[0].id }, // Regular user
			{ chatId: testChats[1].id, userId: testUsers[2].id }, // Clinic
		]);

		// Create test messages with different read statuses
		const testMessages = [];
		
		// Chat 1: Some unread messages from rescuer to regular user
		for (let i = 0; i < 10; i++) {
			testMessages.push({
				chatId: testChats[0].id,
				userId: testUsers[1].id, // From rescuer
				content: `Message from rescuer ${i + 1}`,
				status: i < 3 ? "sent" : "read" as const, // 3 unread messages
			});
		}
		
		// Chat 2: Some unread messages from clinic to regular user
		for (let i = 0; i < 8; i++) {
			testMessages.push({
				chatId: testChats[1].id,
				userId: testUsers[2].id, // From clinic
				content: `Message from clinic ${i + 1}`,
				status: i < 2 ? "sent" : "read" as const, // 2 unread messages
			});
		}

		const insertedMessages = await db.insert(messages).values(testMessages).returning();
		testData.messages = insertedMessages;
	});

	afterAll(async () => {
		// Clean up test data
		await db.delete(messages).where(inArray(messages.id, testData.messages.map(m => m.id)));
		await db.delete(chatParticipants).where(inArray(chatParticipants.chatId, testData.chats.map(c => c.id)));
		await db.delete(chats).where(inArray(chats.id, testData.chats.map(c => c.id)));
		await db.delete(cats).where(inArray(cats.id, testData.cats.map(c => c.id)));
		await db.delete(users).where(inArray(users.id, testData.users.map(u => u.id)));
	});

	it("should fetch chats with unread counts and verification status efficiently", async () => {
		const startTime = performance.now();
		const userId = testData.users[0].id; // Regular user

		// Simulate the enhanced getMyChats query
		// Step 1: Get user's chat IDs
		const userChatIds = await chatHelpers.getUserChatIds(db, userId, 20, 0);
		expect(userChatIds.length).toBe(2);

		const chatIds = userChatIds.map((c: any) => c.chatId);

		// Step 2: Get chat details
		const chatsData = await db.query.chats.findMany({
			where: inArray(chats.id, chatIds),
			with: {
				cat: {
					columns: {
						id: true,
						slug: true,
						name: true,
					},
					with: {
						images: {
							where: eq(catImages.isPrimary, true),
							limit: 1,
						},
					},
				},
				messages: {
					orderBy: desc(messages.createdAt),
					limit: 1,
					columns: {
						id: true,
						content: true,
						createdAt: true,
						status: true,
						userId: true,
					},
				},
			},
		});

		// Step 3: Get other participants
		const otherParticipants = await chatHelpers.getChatParticipants(db, chatIds, userId);

		// Step 4: Get unread message counts
		const unreadCountsMap = await chatHelpers.getBatchUnreadMessageCounts(db, chatIds, userId);

		// Step 5: Get verification status
		const otherUserIds = otherParticipants.map((p: any) => p.user.id);
		const verificationMap = await chatHelpers.getBatchUserVerificationStatus(db, otherUserIds);

		const duration = performance.now() - startTime;
		console.log(`Enhanced getMyChats completed in ${duration.toFixed(2)}ms`);

		// Validate results
		expect(chatsData.length).toBe(2);
		expect(unreadCountsMap.size).toBe(2);
		expect(verificationMap.size).toBe(2);

		// Check unread counts
		const chat1UnreadCount = unreadCountsMap.get(testData.chats[0].id);
		const chat2UnreadCount = unreadCountsMap.get(testData.chats[1].id);
		expect(chat1UnreadCount).toBe(3); // 3 unread messages from rescuer
		expect(chat2UnreadCount).toBe(2); // 2 unread messages from clinic

		// Check verification status
		const rescuerVerified = verificationMap.get(testData.users[1].id);
		const clinicVerified = verificationMap.get(testData.users[2].id);
		expect(rescuerVerified).toBe(true); // Rescuer should be verified
		expect(clinicVerified).toBe(true); // Clinic should be verified

		// Performance should be good
		expect(duration).toBeLessThan(300); // Should complete in under 300ms
	});

	it("should handle empty results gracefully", async () => {
		const startTime = performance.now();
		const userId = 99999; // Non-existent user

		// Test with non-existent user
		const userChatIds = await chatHelpers.getUserChatIds(db, userId, 20, 0);
		const unreadCountsMap = await chatHelpers.getBatchUnreadMessageCounts(db, [], userId);
		const verificationMap = await chatHelpers.getBatchUserVerificationStatus(db, []);

		const duration = performance.now() - startTime;
		console.log(`Empty results handling completed in ${duration.toFixed(2)}ms`);

		expect(userChatIds.length).toBe(0);
		expect(unreadCountsMap.size).toBe(0);
		expect(verificationMap.size).toBe(0);
		expect(duration).toBeLessThan(50); // Should be very fast for empty results
	});

	it("should calculate unread counts correctly for different scenarios", async () => {
		const startTime = performance.now();
		const userId = testData.users[1].id; // Rescuer user
		const chatId = testData.chats[0].id;

		// Get unread count for rescuer (should be 0 since they sent the messages)
		const unreadCount = await chatHelpers.getUnreadMessageCount(db, chatId, userId);

		const duration = performance.now() - startTime;
		console.log(`Unread count calculation completed in ${duration.toFixed(2)}ms`);

		expect(unreadCount).toBe(0); // Rescuer shouldn't have unread messages in their own chat
		expect(duration).toBeLessThan(30); // Should be very fast
	});

	it("should verify user roles correctly", async () => {
		const startTime = performance.now();

		// Test individual verification
		const regularUserVerified = await chatHelpers.isUserVerified(db, testData.users[0].id);
		const rescuerVerified = await chatHelpers.isUserVerified(db, testData.users[1].id);
		const clinicVerified = await chatHelpers.isUserVerified(db, testData.users[2].id);

		const duration = performance.now() - startTime;
		console.log(`User verification completed in ${duration.toFixed(2)}ms`);

		expect(regularUserVerified).toBe(false); // Regular adopter should not be verified
		expect(rescuerVerified).toBe(true); // Rescuer should be verified
		expect(clinicVerified).toBe(true); // Clinic should be verified
		expect(duration).toBeLessThan(100); // Should be fast
	});
});
