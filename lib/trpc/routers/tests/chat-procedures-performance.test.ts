/**
 * Performance comparison test for different chat procedures
 * 
 * This test compares the performance of different chat fetching procedures:
 * - getMyChats (optimized version)
 * - getChatList (lightweight version)
 * - getMyChatsByParticipant (participant-based version)
 */

import { describe, it, expect, beforeAll, afterAll } from "bun:test";
import { db } from "@/lib/db";
import { chats, chatParticipants, messages, users, cats, catImages } from "@/lib/db/schema";
import { eq, inArray, desc, and } from "drizzle-orm";
import { chatHelpers } from "../helpers/chat-helpers";

// Mock data for testing
const testData = {
	users: [] as any[],
	cats: [] as any[],
	chats: [] as any[],
	messages: [] as any[],
};

describe("Chat Procedures Performance Comparison", () => {
	beforeAll(async () => {
		// Create test users
		const testUsers = await db.insert(users).values([
			{
				name: "Test User 1",
				email: "<EMAIL>",
				slug: "test-user-1",
				role: "adopter",
			},
			{
				name: "Test User 2",
				email: "<EMAIL>",
				slug: "test-user-2",
				role: "rescuer",
			},
			{
				name: "Test User 3",
				email: "<EMAIL>",
				slug: "test-user-3",
				role: "clinic",
			},
		]).returning();
		testData.users = testUsers;

		// Create test cats
		const testCats = await db.insert(cats).values([
			{
				name: "Test Cat 1",
				slug: "test-cat-1",
				userId: testUsers[0].id,
				age: 2,
				gender: "male",
				description: "A friendly cat",
				wilayaId: 1,
				communeId: 1,
			},
			{
				name: "Test Cat 2",
				slug: "test-cat-2",
				userId: testUsers[1].id,
				age: 3,
				gender: "female",
				description: "A playful cat",
				wilayaId: 1,
				communeId: 1,
			},
		]).returning();
		testData.cats = testCats;

		// Create test chats
		const testChats = await db.insert(chats).values([
			{ catId: testCats[0].id },
			{ catId: testCats[1].id },
		]).returning();
		testData.chats = testChats;

		// Create chat participants
		await db.insert(chatParticipants).values([
			{ chatId: testChats[0].id, userId: testUsers[0].id },
			{ chatId: testChats[0].id, userId: testUsers[1].id },
			{ chatId: testChats[1].id, userId: testUsers[1].id },
			{ chatId: testChats[1].id, userId: testUsers[2].id },
		]);

		// Create test messages
		const testMessages = [];
		for (let i = 0; i < 50; i++) {
			testMessages.push({
				chatId: testChats[0].id,
				userId: i % 2 === 0 ? testUsers[0].id : testUsers[1].id,
				content: `Test message ${i + 1}`,
				status: i < 10 ? "sent" : "read" as const,
			});
		}
		
		for (let i = 0; i < 30; i++) {
			testMessages.push({
				chatId: testChats[1].id,
				userId: i % 2 === 0 ? testUsers[1].id : testUsers[2].id,
				content: `Test message ${i + 1}`,
				status: i < 5 ? "sent" : "read" as const,
			});
		}

		const insertedMessages = await db.insert(messages).values(testMessages).returning();
		testData.messages = insertedMessages;
	});

	afterAll(async () => {
		// Clean up test data
		await db.delete(messages).where(inArray(messages.id, testData.messages.map(m => m.id)));
		await db.delete(chatParticipants).where(inArray(chatParticipants.chatId, testData.chats.map(c => c.id)));
		await db.delete(chats).where(inArray(chats.id, testData.chats.map(c => c.id)));
		await db.delete(cats).where(inArray(cats.id, testData.cats.map(c => c.id)));
		await db.delete(users).where(inArray(users.id, testData.users.map(u => u.id)));
	});

	it("should compare getMyChats performance (optimized version)", async () => {
		const startTime = performance.now();
		const userId = testData.users[0].id;

		// Simulate the optimized getMyChats query
		// Step 1: Get user's chat IDs efficiently
		const userChatIds = await chatHelpers.getUserChatIds(db, userId, 20, 0);
		
		if (userChatIds.length > 0) {
			const chatIds = userChatIds.map((c: any) => c.chatId);

			// Step 2: Get chat details with optimized joins
			const chatsData = await db.query.chats.findMany({
				where: inArray(chats.id, chatIds),
				with: {
					cat: {
						columns: {
							id: true,
							slug: true,
							name: true,
						},
						with: {
							images: {
								where: eq(catImages.isPrimary, true),
								limit: 1,
							},
						},
					},
					messages: {
						orderBy: desc(messages.createdAt),
						limit: 1,
						columns: {
							id: true,
							content: true,
							createdAt: true,
							status: true,
							userId: true,
						},
					},
				},
			});

			// Step 3: Get other participants
			const otherParticipants = await chatHelpers.getChatParticipants(db, chatIds, userId);
		}

		const duration = performance.now() - startTime;
		console.log(`getMyChats (optimized) completed in ${duration.toFixed(2)}ms`);
		
		expect(duration).toBeLessThan(200); // Should complete in under 200ms
	});

	it("should compare getChatList performance (lightweight version)", async () => {
		const startTime = performance.now();
		const userId = testData.users[0].id;

		// Simulate the lightweight getChatList query
		const userChatIds = await chatHelpers.getUserChatIds(db, userId, 20, 0);
		
		if (userChatIds.length > 0) {
			const chatIds = userChatIds.map((c: any) => c.chatId);

			// Get minimal chat data with only necessary fields
			const chatsData = await db.query.chats.findMany({
				where: inArray(chats.id, chatIds),
				columns: {
					id: true,
					catId: true,
					createdAt: true,
				},
				with: {
					cat: {
						columns: {
							id: true,
							slug: true,
							name: true,
						},
						with: {
							images: {
								where: eq(catImages.isPrimary, true),
								limit: 1,
								columns: {
									url: true,
									isPrimary: true,
								},
							},
						},
					},
					messages: {
						orderBy: desc(messages.createdAt),
						limit: 1,
						columns: {
							id: true,
							content: true,
							createdAt: true,
							status: true,
							userId: true,
						},
					},
				},
			});
		}

		const duration = performance.now() - startTime;
		console.log(`getChatList (lightweight) completed in ${duration.toFixed(2)}ms`);
		
		expect(duration).toBeLessThan(150); // Should be faster than getMyChats
	});

	it("should compare getMyChatsByParticipant performance", async () => {
		const startTime = performance.now();
		const userId = testData.users[1].id;

		// Simulate the getMyChatsByParticipant query
		const userChats = await db.query.chatParticipants.findMany({
			where: eq(chatParticipants.userId, userId),
			with: {
				chat: {
					with: {
						cat: {
							with: {
								images: true,
								user: true,
							},
						},
						participants: {
							with: {
								user: true,
							},
						},
						messages: {
							orderBy: desc(messages.createdAt),
							limit: 1,
						},
					},
				},
			},
			orderBy: desc(chatParticipants.createdAt),
			limit: 10,
		});

		const duration = performance.now() - startTime;
		console.log(`getMyChatsByParticipant completed in ${duration.toFixed(2)}ms`);
		
		expect(duration).toBeLessThan(300); // This might be slower due to heavy joins
		expect(userChats.length).toBeGreaterThan(0);
	});

	it("should test unread message count calculation", async () => {
		const startTime = performance.now();
		const userId = testData.users[0].id;
		const chatId = testData.chats[0].id;

		// Get unread messages for the user
		const unreadMessages = await db.query.messages.findMany({
			where: and(
				eq(messages.chatId, chatId),
				eq(messages.status, "sent")
			),
			columns: {
				id: true,
				userId: true,
			},
		});

		// Count unread messages not from the current user
		const unreadCount = unreadMessages.filter(msg => msg.userId !== userId).length;

		const duration = performance.now() - startTime;
		console.log(`Unread count calculation completed in ${duration.toFixed(2)}ms, found ${unreadCount} unread messages`);
		
		expect(duration).toBeLessThan(50); // Should be very fast
		expect(unreadCount).toBeGreaterThanOrEqual(0);
	});

	it("should test user verification status lookup", async () => {
		const startTime = performance.now();
		const userId = testData.users[2].id; // Clinic user

		// Get user role for verification
		const user = await db.query.users.findFirst({
			where: eq(users.id, userId),
			columns: {
				id: true,
				role: true,
				name: true,
			},
		});

		const isVerified = user?.role === "clinic" || user?.role === "rescuer";

		const duration = performance.now() - startTime;
		console.log(`User verification lookup completed in ${duration.toFixed(2)}ms, verified: ${isVerified}`);
		
		expect(duration).toBeLessThan(20); // Should be very fast
		expect(user).toBeDefined();
		expect(isVerified).toBe(true); // Clinic user should be verified
	});
});
